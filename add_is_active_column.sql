-- Script para adicionar a coluna is_active na tabela jobs
-- Execute este script no SQL Editor do Supabase Dashboard

-- Adicionar a coluna is_active se ela não existir
ALTER TABLE public.jobs 
ADD COLUMN IF NOT EXISTS is_active BOOLEAN NOT NULL DEFAULT true;

-- Adicionar comentário para explicar o campo
COMMENT ON COLUMN public.jobs.is_active IS 'Flag to mark if job is still available on the source website';

-- <PERSON><PERSON><PERSON> índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_jobs_is_active ON public.jobs(is_active);
CREATE INDEX IF NOT EXISTS idx_jobs_active_created_at ON public.jobs(is_active, created_at DESC);

-- Verificar se a coluna foi criada corretamente
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'jobs' AND column_name = 'is_active';
