import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface ScrapedJob {
  external_id: string;
  title: string;
  description: string;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log('Starting HardFranca scraping...');

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Fetch the HardFranca page
    const response = await fetch('https://hardfranca.com.br/anuncios.php');
    
    if (!response.ok) {
      throw new Error(`Failed to fetch HardFranca page: ${response.status}`);
    }

    const html = await response.text();
    console.log('Successfully fetched HardFranca page');

    // Parse HTML and extract job data
    const jobs = extractJobsFromHtml(html);
    console.log(`Extracted ${jobs.length} jobs from HTML`);

    if (jobs.length === 0) {
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'No jobs found on the page',
          jobs_processed: 0 
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json", ...corsHeaders },
        }
      );
    }

    // Insert jobs into database
    let successCount = 0;
    let errorCount = 0;

    for (const job of jobs) {
      try {
        const { error } = await supabase
          .from('jobs')
          .upsert({
            external_id: job.external_id,
            title: job.title,
            description: job.description,
            location: 'Franca, SP',
            category: 'Geral',
            company: 'HardFranca'
          }, {
            onConflict: 'external_id'
          });

        if (error) {
          console.error(`Error inserting job ${job.external_id}:`, error);
          errorCount++;
        } else {
          console.log(`Successfully inserted/updated job ${job.external_id}`);
          successCount++;
        }
      } catch (error) {
        console.error(`Exception inserting job ${job.external_id}:`, error);
        errorCount++;
      }
    }

    console.log(`Scraping completed: ${successCount} success, ${errorCount} errors`);

    return new Response(
      JSON.stringify({
        success: true,
        message: `Scraping completed successfully`,
        jobs_processed: successCount,
        jobs_failed: errorCount,
        total_found: jobs.length
      }),
      {
        status: 200,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );

  } catch (error: any) {
    console.error('Error in scrape-hardfranca function:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

function extractJobsFromHtml(html: string): ScrapedJob[] {
  const jobs: ScrapedJob[] = [];
  
  try {
    console.log('Starting HTML parsing...');
    
    // Find all job containers with pattern anuncio_completo_dest{CODE}
    // Using a more flexible regex pattern
    const jobRegex = /<div[^>]*id="anuncio_completo_dest(\d+)"[^>]*>([\s\S]*?)(?=<div[^>]*id="anuncio_completo_dest\d+"|<\/div>\s*<\/div>\s*$)/g;
    
    let match;
    let jobCount = 0;
    
    while ((match = jobRegex.exec(html)) !== null) {
      const externalId = match[1];
      const jobHtml = match[2];
      jobCount++;
      
      console.log(`Processing job ${jobCount} with ID: ${externalId}`);
      
      // Extract title from div with class "titulo-anuncio"
      const titleRegex = /<div[^>]*class="titulo-anuncio"[^>]*>(.*?)<\/div>/s;
      const titleMatch = jobHtml.match(titleRegex);
      
      if (!titleMatch) {
        console.log(`No title found for job ${externalId}`);
        continue;
      }
      
      const title = cleanHtmlText(titleMatch[1]);
      console.log(`Found title: ${title}`);
      
      // Try multiple patterns for description
      let description = '';
      
      // Pattern 1: div with style containing "line-height: 20px" after title
      const descPattern1 = /<div[^>]*class="titulo-anuncio"[^>]*>.*?<\/div>\s*<div[^>]*style="[^"]*line-height:\s*20px[^"]*"[^>]*>(.*?)<\/div>/s;
      let descMatch = jobHtml.match(descPattern1);
      
      if (descMatch) {
        description = cleanHtmlText(descMatch[1]);
        console.log(`Found description with pattern 1: ${description.substring(0, 100)}...`);
      } else {
        // Pattern 2: any div immediately after title div
        const descPattern2 = /<div[^>]*class="titulo-anuncio"[^>]*>.*?<\/div>\s*<div[^>]*>(.*?)<\/div>/s;
        descMatch = jobHtml.match(descPattern2);
        
        if (descMatch) {
          description = cleanHtmlText(descMatch[1]);
          console.log(`Found description with pattern 2: ${description.substring(0, 100)}...`);
        } else {
          // Pattern 3: Look for any text content after the title
          const afterTitle = jobHtml.substring(jobHtml.indexOf('</div>') + 6);
          const textMatch = afterTitle.match(/>\s*([^<]+)/);
          if (textMatch) {
            description = cleanHtmlText(textMatch[1]);
            console.log(`Found description with pattern 3: ${description.substring(0, 100)}...`);
          }
        }
      }
      
      if (title && description && description.length > 10) {
        jobs.push({
          external_id: externalId,
          title: title,
          description: description
        });
        
        console.log(`Successfully extracted job: ${externalId} - ${title.substring(0, 50)}...`);
      } else {
        console.log(`Incomplete data for job ${externalId}: title=${!!title}, description=${!!description} (length: ${description.length})`);
        console.log(`Title: "${title}"`);
        console.log(`Description: "${description}"`);
      }
    }
    
    console.log(`Total jobs processed: ${jobCount}, Successfully extracted: ${jobs.length}`);
    
    // If no jobs found with the main pattern, try alternative approach
    if (jobs.length === 0) {
      console.log('No jobs found with main pattern, trying alternative approach...');
      
      // Look for any div with "titulo-anuncio" class
      const titleElements = html.match(/<div[^>]*class="titulo-anuncio"[^>]*>.*?<\/div>/g);
      console.log(`Found ${titleElements?.length || 0} title elements`);
      
      if (titleElements) {
        titleElements.forEach((titleElement, index) => {
          const title = cleanHtmlText(titleElement.replace(/<[^>]*>/g, ''));
          console.log(`Alternative extraction ${index + 1}: ${title}`);
        });
      }
    }
    
  } catch (error) {
    console.error('Error parsing HTML:', error);
  }
  
  return jobs;
}

function cleanHtmlText(text: string): string {
  return text
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&nbsp;/g, ' ') // Replace &nbsp; with space
    .replace(/&amp;/g, '&') // Replace &amp; with &
    .replace(/&lt;/g, '<') // Replace &lt; with <
    .replace(/&gt;/g, '>') // Replace &gt; with >
    .replace(/&quot;/g, '"') // Replace &quot; with "
    .replace(/&#39;/g, "'") // Replace &#39; with '
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim(); // Remove leading/trailing whitespace
}

serve(handler);