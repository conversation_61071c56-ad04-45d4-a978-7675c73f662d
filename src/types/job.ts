export interface Job {
  id: string;
  title: string;
  company: string;
  description: string;
  requirements: string | string[];
  location: string;
  salary?: string;
  contractType: 'full-time' | 'part-time' | 'contract' | 'freelance' | 'internship';
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  category: string;
  isUrgent?: boolean;
  isFeatured?: boolean;
  postedDate: Date;
  applicationUrl?: string;
  companyLogo?: string;
  source?: 'manual' | 'hardfranca';
  // Alternative property names for backward compatibility
  type?: string;
  experience?: string;
  postedAt?: string;
}

export interface JobFilters {
  search: string;
  category: string;
  location: string;
  contractType: string;
  experienceLevel: string;
  salaryMin?: number;
  salaryMax?: number;
}

export type SortOption = 'newest' | 'oldest' | 'relevance' | 'salary-high' | 'salary-low';