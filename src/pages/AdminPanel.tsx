import { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Download, Plus, Trash2, Edit, Globe, RefreshCw, Eye } from "lucide-react";
import { Link } from "react-router-dom";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { supabase } from "@/integrations/supabase/client";

interface Job {
  id: string;
  external_id?: string;
  title: string;
  company: string;
  location: string;
  category: string;
  description: string;
  created_at: string;
}

// Component for viewing job details
const JobViewDialog = ({ job, isOpen, onClose }: { job: Job; isOpen: boolean; onClose: () => void }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Detalhes da Vaga</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Título</Label>
              <p className="text-sm">{job.title}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Empresa</Label>
              <p className="text-sm">{job.company}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Localização</Label>
              <p className="text-sm">{job.location}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Categoria</Label>
              <p className="text-sm">{job.category}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Data de Criação</Label>
              <p className="text-sm">{new Date(job.created_at).toLocaleDateString('pt-BR')}</p>
            </div>
            {job.external_id && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">ID Externo</Label>
                <p className="text-sm">{job.external_id}</p>
              </div>
            )}
          </div>
          <div>
            <Label className="text-sm font-medium text-muted-foreground">Descrição</Label>
            <div className="mt-2 p-3 bg-muted/50 rounded-lg">
              <p className="text-sm whitespace-pre-wrap">{job.description}</p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Component for editing job
const JobEditDialog = ({ job, isOpen, onClose, onSave }: { 
  job: Job; 
  isOpen: boolean; 
  onClose: () => void; 
  onSave: (updatedJob: Partial<Job>) => void;
}) => {
  const [editForm, setEditForm] = useState({
    title: job.title,
    company: job.company,
    location: job.location,
    category: job.category,
    description: job.description
  });

  const handleSave = () => {
    onSave(editForm);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Editar Vaga</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit-title">Título da Vaga</Label>
              <Input
                id="edit-title"
                value={editForm.title}
                onChange={(e) => setEditForm(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-company">Empresa</Label>
              <Input
                id="edit-company"
                value={editForm.company}
                onChange={(e) => setEditForm(prev => ({ ...prev, company: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-location">Localização</Label>
              <Input
                id="edit-location"
                value={editForm.location}
                onChange={(e) => setEditForm(prev => ({ ...prev, location: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-category">Categoria</Label>
              <Input
                id="edit-category"
                value={editForm.category}
                onChange={(e) => setEditForm(prev => ({ ...prev, category: e.target.value }))}
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="edit-description">Descrição</Label>
            <Textarea
              id="edit-description"
              value={editForm.description}
              onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
              className="min-h-[120px]"
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose}>Cancelar</Button>
            <Button onClick={handleSave}>Salvar Alterações</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const AdminPanel = () => {
  const { toast } = useToast();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [isScrapingJobs, setIsScrapingJobs] = useState(false);
  const [isLoadingJobs, setIsLoadingJobs] = useState(false);
  const [scrapingResults, setScrapingResults] = useState<{ success: number; failed: number; total: number } | null>(null);
  
  // Dialog states
  const [viewingJob, setViewingJob] = useState<Job | null>(null);
  const [editingJob, setEditingJob] = useState<Job | null>(null);

  // Load jobs from Supabase on component mount
  useEffect(() => {
    loadJobs();
  }, []);

  const loadJobs = async () => {
    setIsLoadingJobs(true);
    try {
      const { data, error } = await supabase
        .from('jobs')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading jobs:', error);
        toast({
          title: "Erro ao carregar vagas",
          description: "Não foi possível carregar as vagas do banco de dados",
          variant: "destructive",
        });
      } else {
        setJobs(data || []);
      }
    } catch (error) {
      console.error('Exception loading jobs:', error);
    } finally {
      setIsLoadingJobs(false);
    }
  };
  
  // Form state for manual job creation
  const [jobForm, setJobForm] = useState({
    title: '',
    company: '',
    location: '',
    type: '',
    experience: '',
    description: '',
    requirements: '',
    category: ''
  });

  const handleScrapeJobs = async () => {
    setIsScrapingJobs(true);
    setScrapingResults(null);
    
    try {
      console.log('Starting web scraping...');
      
      // Call the Supabase Edge Function for web scraping
      const { data, error } = await supabase.functions.invoke('scrape-hardfranca');
      
      if (error) {
        console.error('Error calling scrape function:', error);
        throw new Error(error.message || 'Failed to call scraping function');
      }
      
      console.log('Scraping response:', data);
      
      if (data.success) {
        setScrapingResults({
          success: data.jobs_imported || 0,
          failed: data.jobs_failed || 0,
          total: data.total_found || 0
        });

        // Reload jobs to show the new scraped data
        await loadJobs();

        const importedCount = data.jobs_imported || 0;
        const updatedCount = data.jobs_updated || 0;
        const deactivatedCount = data.jobs_deactivated || 0;

        let description = `${importedCount} vagas novas importadas`;
        if (updatedCount > 0) {
          description += `, ${updatedCount} atualizadas`;
        }
        if (deactivatedCount > 0) {
          description += `, ${deactivatedCount} desativadas`;
        }

        toast({
          title: "Scraping concluído",
          description: description,
        });
      } else {
        throw new Error(data.error || 'Scraping failed');
      }
      
    } catch (error: any) {
      console.error('Exception during scraping:', error);
      toast({
        title: "Erro no scraping",
        description: error.message || "Falha ao importar vagas. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsScrapingJobs(false);
    }
  };

  const handleCreateJob = async () => {
    if (!jobForm.title || !jobForm.company || !jobForm.description) {
      toast({
        title: "Campos obrigatórios",
        description: "Preencha pelo menos título, empresa e descrição",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('jobs')
        .insert({
          title: jobForm.title,
          company: jobForm.company,
          location: jobForm.location || 'Franca, SP',
          description: jobForm.description,
          category: jobForm.category || 'Geral',
          external_id: `manual-${Date.now()}`,
          is_active: true
        });

      if (error) {
        console.error('Error creating job:', error);
        toast({
          title: "Erro ao criar vaga",
          description: "Não foi possível salvar a vaga no banco de dados",
          variant: "destructive",
        });
        return;
      }

      // Reload jobs to show the new job
      await loadJobs();
      
      setJobForm({
        title: '',
        company: '',
        location: '',
        type: '',
        experience: '',
        description: '',
        requirements: '',
        category: ''
      });

      toast({
        title: "Vaga criada",
        description: "Nova vaga adicionada com sucesso",
      });
    } catch (error) {
      console.error('Exception creating job:', error);
      toast({
        title: "Erro ao criar vaga",
        description: "Falha inesperada ao salvar a vaga",
        variant: "destructive",
      });
    }
  };

  const handleDeleteJob = async (id: string) => {
    try {
      const { error } = await supabase
        .from('jobs')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting job:', error);
        toast({
          title: "Erro ao deletar vaga",
          description: "Não foi possível deletar a vaga",
          variant: "destructive",
        });
        return;
      }

      // Reload jobs to reflect the deletion
      await loadJobs();
      
      toast({
        title: "Vaga removida",
        description: "Vaga deletada com sucesso",
      });
    } catch (error) {
      console.error('Exception deleting job:', error);
      toast({
        title: "Erro ao deletar vaga",
        description: "Falha inesperada ao deletar a vaga",
        variant: "destructive",
      });
    }
  };

  const handleEditJob = async (jobId: string, updatedData: Partial<Job>) => {
    try {
      const { error } = await supabase
        .from('jobs')
        .update({
          title: updatedData.title,
          company: updatedData.company,
          location: updatedData.location,
          category: updatedData.category,
          description: updatedData.description
        })
        .eq('id', jobId);

      if (error) {
        console.error('Error updating job:', error);
        toast({
          title: "Erro ao editar vaga",
          description: "Não foi possível salvar as alterações",
          variant: "destructive",
        });
        return;
      }

      // Reload jobs to reflect the changes
      await loadJobs();
      
      toast({
        title: "Vaga atualizada",
        description: "Alterações salvas com sucesso",
      });
    } catch (error) {
      console.error('Exception updating job:', error);
      toast({
        title: "Erro ao editar vaga",
        description: "Falha inesperada ao salvar as alterações",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-accent/5">
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link to="/" className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors">
                <ArrowLeft className="h-4 w-4" />
                Voltar ao Portal
              </Link>
              <div className="h-6 w-px bg-border" />
              <h1 className="text-2xl font-bold">Painel Administrativo</h1>
            </div>
            <Badge variant="secondary">Admin</Badge>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <Tabs defaultValue="scraping" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="scraping">Web Scraping</TabsTrigger>
            <TabsTrigger value="create">Criar Vaga</TabsTrigger>
            <TabsTrigger value="manage">Gerenciar Vagas</TabsTrigger>
          </TabsList>

          <TabsContent value="scraping" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  Web Scraping - HardFranca
                </CardTitle>
                <CardDescription>
                  Importe vagas automaticamente do site https://hardfranca.com.br/anuncios.php
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4">
                  <Button 
                    onClick={handleScrapeJobs}
                    disabled={isScrapingJobs}
                    className="bg-primary hover:bg-primary/90"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    {isScrapingJobs ? "Importando..." : "Importar Vagas"}
                  </Button>
                  
                  {scrapingResults && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        {scrapingResults.success} importadas
                      </Badge>
                      {scrapingResults.failed > 0 && (
                        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                          {scrapingResults.failed} falharam
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
                
                <div className="p-4 bg-muted/50 rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    <strong>Como funciona:</strong> O sistema extrai vagas da estrutura HTML do HardFranca, 
                    capturando títulos (classe "titulo-anuncio") e descrições completas. 
                    Duplicatas são automaticamente verificadas antes da importação.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="create" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-5 w-5" />
                  Criar Nova Vaga
                </CardTitle>
                <CardDescription>
                  Adicione uma nova vaga manualmente ao sistema
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Título da Vaga *</Label>
                    <Input
                      id="title"
                      value={jobForm.title}
                      onChange={(e) => setJobForm(prev => ({ ...prev, title: e.target.value }))}
                      placeholder="Ex: Desenvolvedor Frontend"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="company">Empresa *</Label>
                    <Input
                      id="company"
                      value={jobForm.company}
                      onChange={(e) => setJobForm(prev => ({ ...prev, company: e.target.value }))}
                      placeholder="Ex: Tech Company Ltda"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="location">Localização</Label>
                    <Input
                      id="location"
                      value={jobForm.location}
                      onChange={(e) => setJobForm(prev => ({ ...prev, location: e.target.value }))}
                      placeholder="Ex: Franca, SP"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="category">Categoria</Label>
                    <Input
                      id="category"
                      value={jobForm.category}
                      onChange={(e) => setJobForm(prev => ({ ...prev, category: e.target.value }))}
                      placeholder="Ex: Tecnologia"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Tipo de Contrato</Label>
                    <Select value={jobForm.type} onValueChange={(value) => setJobForm(prev => ({ ...prev, type: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="full-time">Tempo Integral</SelectItem>
                        <SelectItem value="part-time">Meio Período</SelectItem>
                        <SelectItem value="contract">Contrato</SelectItem>
                        <SelectItem value="freelance">Freelance</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Nível de Experiência</Label>
                    <Select value={jobForm.experience} onValueChange={(value) => setJobForm(prev => ({ ...prev, experience: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o nível" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="entry">Iniciante</SelectItem>
                        <SelectItem value="mid">Pleno</SelectItem>
                        <SelectItem value="senior">Senior</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Descrição da Vaga *</Label>
                  <Textarea
                    id="description"
                    value={jobForm.description}
                    onChange={(e) => setJobForm(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Descreva as responsabilidades e detalhes da vaga..."
                    className="min-h-[100px]"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="requirements">Requisitos</Label>
                  <Textarea
                    id="requirements"
                    value={jobForm.requirements}
                    onChange={(e) => setJobForm(prev => ({ ...prev, requirements: e.target.value }))}
                    placeholder="Liste os requisitos necessários..."
                    className="min-h-[80px]"
                  />
                </div>
                
                <Button onClick={handleCreateJob} className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  Criar Vaga
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="manage" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Vagas Cadastradas ({jobs.length})</CardTitle>
                <CardDescription>
                  Gerencie todas as vagas do sistema
                </CardDescription>
              </CardHeader>
              <CardContent>
                {jobs.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>Nenhuma vaga cadastrada ainda.</p>
                    <p className="text-sm">Use o web scraping ou crie uma vaga manualmente.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {jobs.map((job) => (
                      <div key={job.id} className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="font-semibold">{job.title}</h3>
                            <p className="text-sm text-muted-foreground">{job.company} • {job.location}</p>
                             <div className="flex items-center gap-2 mt-2">
                               <Badge variant="outline">{job.category}</Badge>
                               <Badge variant="secondary" className={job.external_id?.startsWith('manual-') ? 'bg-green-50 text-green-700' : 'bg-blue-50 text-blue-700'}>
                                 {job.external_id?.startsWith('manual-') ? 'Manual' : 'Importada'}
                               </Badge>
                               {job.external_id && !job.external_id.startsWith('manual-') && (
                                 <Badge variant="outline" className="text-xs">
                                   ID: {job.external_id}
                                 </Badge>
                               )}
                             </div>
                          </div>
                           <div className="flex items-center gap-2">
                             <Button 
                               variant="outline" 
                               size="sm"
                               onClick={() => setViewingJob(job)}
                             >
                               <Eye className="h-4 w-4" />
                             </Button>
                             <Button 
                               variant="outline" 
                               size="sm"
                               onClick={() => setEditingJob(job)}
                             >
                               <Edit className="h-4 w-4" />
                             </Button>
                             <Button 
                               variant="outline" 
                               size="sm"
                               onClick={() => handleDeleteJob(job.id)}
                               className="text-red-600 hover:text-red-700"
                             >
                               <Trash2 className="h-4 w-4" />
                             </Button>
                           </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      {/* View Job Dialog */}
      {viewingJob && (
        <JobViewDialog
          job={viewingJob}
          isOpen={!!viewingJob}
          onClose={() => setViewingJob(null)}
        />
      )}

      {/* Edit Job Dialog */}
      {editingJob && (
        <JobEditDialog
          job={editingJob}
          isOpen={!!editingJob}
          onClose={() => setEditingJob(null)}
          onSave={(updatedData) => handleEditJob(editingJob.id, updatedData)}
        />
      )}
    </div>
  );
};

export default AdminPanel;